package com.pgb.common.core.global;

/**
 * 全局 key 常量
 */
public interface GlobalConstants {
    /**
     * 全局 redis key (业务无关的key)
     */
    String GLOBAL_REDIS_KEY = "global:";

    /**
     * 验证码 redis key
     */
    String CAPTCHA_CODE_KEY = GLOBAL_REDIS_KEY + "captcha_codes:";

    /**
     * 店铺管理端-短信验证码 redis key
     */
    String ADMIN_CAPTCHA_SMS_CODE_KEY = CAPTCHA_CODE_KEY + "sms:";

    /**
     * 用户端-短信验证码 redis key
     */
    String USER_CAPTCHA_SMS_CODE_KEY = CAPTCHA_CODE_KEY + "sms:";

    /**
     * 订单过期队列
     */
    String ORDER_EXPIRE_QUEUE_KEY = GLOBAL_REDIS_KEY + "order_expire_queue";

    /**
     * 店铺 OCR 次数限制
     */
    String TENANT_OCR_LIMIT_REDIS_KEY = GLOBAL_REDIS_KEY + "tenant_ocr_limit:";

    /**
     * 生成海报缓存
     */
    String POSTER_CACHE_KEY = GLOBAL_REDIS_KEY + "poster_cache:";

    /**
     * 【小程序】大模型调用次数限制
     */
    String PGB_MODEL_LIMIT_REDIS_KEY = GLOBAL_REDIS_KEY + "pgb_model_limit:";

    /**
     * 【小程序-跳转】 获取加密URLLink缓存
     */
    String PGB_URL_LINK_KEY = GLOBAL_REDIS_KEY + "pgb_url_link:";

    /**
     * 【小程序】 版本信息缓存
     */
    String XCX_VERSION_INFO_KEY = GLOBAL_REDIS_KEY + "xcx_version_info:";

}
