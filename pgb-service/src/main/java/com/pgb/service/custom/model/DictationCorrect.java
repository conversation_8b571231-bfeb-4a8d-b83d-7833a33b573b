package com.pgb.service.custom.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRChars;
import com.pgb.common.ocr.domain.OCRResult;
import com.pgb.common.ocr.domain.OCRWordResult;
import com.pgb.service.custom.model.util.NlpUtil;
import com.pgb.service.custom.model.util.OcrUtil;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.common.MarkLoc;
import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.domain.zc.question.chinese.dictation.ZcDictation;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.factory.LLMServiceFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/6/18 18:17
 */
@Component
@RequiredArgsConstructor
public class DictationCorrect {

    private final OCRService ocrService;

    private final LLMServiceFactory llmServiceFactory;

    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("ali");
    }

    public PinyinAndWordResult correct(PgZcAnswer zcAnswer) {

        // 初始化批改结果
        PinyinAndWordResult result = new PinyinAndWordResult();

        // 用户答案
        ZcSubmitForm form = JSONUtil.toBean(JSONUtil.toJsonStr(zcAnswer.getAnswer()), ZcSubmitForm.class);

        // 获取题目json
        String questionJsonUrl = form.getQuestionJsonUrl();

        String jsonContent = HttpUtil.get(questionJsonUrl);

        PgZcQuestion question = JSONUtil.toBean(jsonContent, PgZcQuestion.class);

        // 获取听写/默写课文内容
        ZcDictation zcDictation = JSONUtil.toBean(JSONUtil.toJsonStr(question.getContentJson()), ZcDictation.class);

        // 答案内容
        String text = zcDictation.getText();

        if (StrUtil.isNotBlank(text)) {
            // 按空格分隔
            String[] standardWords = text.split("\\s+");

            // 构建返回参数
            List<List<WordItem>> wordList = new ArrayList<>();

            // ocr识别用户答案
            for (int i = 0; i < form.getUserImgAnswerList().size(); i++) {

                OCRResult ocrResult = ocrService.handWriting(form.getUserImgAnswerList().get(i).getImgUrl());
                List<OCRWordResult> userAnswers = ocrResult.getWords_result();

                // 先构建标准答案的 WordItem 列表，初始状态为未作答
                List<WordItem> standardItemList = new ArrayList<>();
                for (String standardWord : standardWords) {
                    WordItem item = new WordItem();
                    item.setWord(standardWord);
                    // 默认未作答
//                    item.setRightType(0);
                    standardItemList.add(item);
                }

                // 匹配 OCR 内容并设置用户作答信息
                List<WordItem> wordItems = llmCorrect(text, userAnswers, standardItemList, i);

                wordList.add(wordItems);
            }

            // 返回批改结果
            result.setUserWordList(wordList);
            result.setUserImgAnswerList(form.getUserImgAnswerList());

            zcAnswer.setCorrectResult(result);
        }

        return result;
    }

    /**
     * 使用大模型进行批改，返回正误情况
     *
     * @param answer
     * @param userOcr
     * @param standardItemList
     * @param pageNo
     * @return
     */
    private List<WordItem> llmCorrect(String answer, List<OCRWordResult> userOcr, List<WordItem> standardItemList, Integer pageNo) {

        // 构建返回参数
        List<WordItem> wordItemList = new ArrayList<>();

        // 构建 用户ocr字符串
        StringBuilder userStrBuilder = new StringBuilder();
        List<OCRChars> userOcrCharList = new ArrayList<>();
        List<String> userOcrCharStrList = new ArrayList<>();

        for (OCRWordResult wordResult : userOcr) {
            userStrBuilder.append(
                    wordResult.getWords()
            ).append(" ");

            userOcrCharList.addAll(wordResult.getChars());

            userOcrCharStrList.addAll(
                    wordResult.getChars().stream().map(OCRChars::getChars).toList()
            );
        }

        String prompt = """
                你需要对提供的学生作答内容（OCR后），进行语文字词批改。
                你只需要返回批改后的内容，不要返回任何无关内容和解释内容。
                                
                【返回要求，请严格遵守，每行为一个单词批改情况】
                答案中匹配的对应语文词语（答案中的）[空格间隔]学生语文字词原文（即使词语是错误的）[空格间隔]正确或错误
                                
                【示例】
                晨 晨 正确
                你好 您好 错误
                """;

        List<String> userList = new ArrayList<>();
        userList.add(
                StrUtil.format("""
                        这是需要默写的正确答案：
                        {}
                        """, answer)
        );
        userList.add(StrUtil.format("""
                这是学生默写后的内容（OCR识别后）：
                {}
                """, userStrBuilder.toString())
        );

        LLMService llmService = getLLMService();
        GPTAnswer llmAnswer = llmService.chatComplete(
                prompt, userList, false, null
        );
        // 对 llmAnswer 进行处理，一行一个
        List<String> stringList = StrUtil.split(llmAnswer.getAnswer(), "\n");
        for (String s : stringList) {

            List<String> correctSplit = StrUtil.split(s, " ");
            if (correctSplit.size() < 3) {
                continue;
            }

            // 正确答案
            String rightWord = correctSplit.get(0);
            if (StrUtil.isBlank(rightWord)) {
                continue;
            }

            // 用户作答
            String word = correctSplit.get(1);
            if (StrUtil.isBlank(word)) {
                continue;
            }

            // 正误
            String rightType = correctSplit.get(2);
            boolean right = rightType.contains("正确");

            // 在答案中寻找
            WordItem wordItem = CollUtil.findOne(standardItemList, item -> item.getWord().equals(rightWord));
            if (ObjectUtil.isNull(wordItem)) {
                continue;
            }

            // 找到符合的ocr字符串
            int subStringIndex = NlpUtil.findEnglishSubString(userOcrCharStrList, word);

            // 说明找不到坐标 -> 未作答
            if (subStringIndex == -1) {

                wordItem.setUserContent(word);
//                wordItem.setRightType(0);
                wordItemList.add(wordItem);
                continue;
            }
            // 找到了,设置用户作答对应坐标
            List<OCRChars> sub = CollUtil.sub(userOcrCharList, subStringIndex, subStringIndex + word.length());
            ZcLocation location = OcrUtil.areaLocation(sub, sub.size());

            // 设置用户作答对应坐标
            if (ObjectUtil.isNotNull(location)) {
                location.setPageNo(pageNo);
                wordItem.setLocation(location);
                // 标注位置就是作答位置
                MarkLoc markLoc = new MarkLoc();
                markLoc.setRightType(right ? 1 : 2);
                markLoc.setZcLocation(location);
                // 设置错别字
                if (!right){
                    markLoc.setErrorWord(word);
                }
                List<MarkLoc> markLocationList = new ArrayList<>();
                markLocationList.add(markLoc);
                wordItem.setMarkLocation(markLocationList);
            }
            wordItem.setUserContent(word);

            // 判断是否正确
//            wordItem.setRightType(right ? 1 : 2);

            wordItemList.add(wordItem);

        }
        return wordItemList;
    }


}
