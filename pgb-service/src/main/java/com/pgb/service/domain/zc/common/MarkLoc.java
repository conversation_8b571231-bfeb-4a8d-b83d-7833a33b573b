package com.pgb.service.domain.zc.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created by 2025/7/3 18:28
 */
@Data
public class MarkLoc {

    @Schema(title = "标注坐标")
    private ZcLocation zcLocation;

    @Schema(title = "批改正误情况：0：未作答，1：正确，2：错误，3：无需标注")
    private Integer rightType;

    @Schema(title = "错别字的索引",description = "基于整个WordItem的索引")
    private Integer index;

    @Schema(title = "错别字")
    private String errorWord;

}
