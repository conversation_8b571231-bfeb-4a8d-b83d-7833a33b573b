package com.pgb.xcx.controller.zc.question.english;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.GlobalConstants;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.RedisUtils;
import com.pgb.service.custom.ProcessImgUtil;
import com.pgb.service.db.PgZcEngWordCommonService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.dictation.ZcDictation;
import com.pgb.service.domain.zc.question.english.dictation.EnDictation;
import com.pgb.service.domain.zc.word.english.EngWordForm;
import com.pgb.service.domain.zc.word.english.EngWordItem;
import com.pgb.service.domain.zc.word.english.common.PgZcEngWordCommon;
import com.pgb.service.domain.zc.word.english.common.PgZcEngWordCommonVO;
import com.pgb.service.enums.ExportStatusEnum;
import com.pgb.service.enums.GenerateStatusEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import com.pgb.service.factory.LLMServiceFactory;
import com.pgb.xcx.common.UserBehaviorUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * Created by 2025/6/6 18:13
 */
@Tag(name = "用户端/字词/题目/英语/单词听写")
@RestController("EnglishDictationController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/question/english/dictation")
@RequiredArgsConstructor
@Slf4j
public class DictationController {

    private final PgZcQuestionService pgZcQuestionService;

    private final PgZcEngWordCommonService pgZcEngWordCommonService;

    private final OssService ossService;

    private final LLMServiceFactory llmServiceFactory;

    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("ali");
    }

    @Operation(summary = "新增题目【单词听写】")
    @PostMapping("save")
    @SaCheckLogin
    public BaseResult<Long> save(@RequestBody EnDictation form) {

        // 保存题目基本信息
        PgZcQuestion zcQuestion = new PgZcQuestion();

        // 用户id
        zcQuestion.setUserId(StpUtil.getLoginIdAsLong());
        // 科目
        zcQuestion.setSubject(SubjectEnum.English);
        // 字词类型
        zcQuestion.setType(ZcQuestionTypeEnum.EnDictation);

        // 分数，默认100分
        zcQuestion.setScore(100);
        // 题目名称
        zcQuestion.setName(
                StrUtil.isBlank(form.getName()) ? "英语听写" : form.getName()
        );
        // 年级，暂时不写
        // 是否官方
        zcQuestion.setIsOfficial(false);
        zcQuestion.setCreateTime(new Date());
        zcQuestion.setUpdateTime(new Date());
        // 保存题目内容
        zcQuestion.setContentJson(
                form
        );
        // 未生成pdf
        zcQuestion.setPdfStatus(ExportStatusEnum.Init);

        pgZcQuestionService.save(zcQuestion);

        // 异步执行获取英语单词音标信息
        if (ObjectUtil.isNotNull(form.getText())) {

            pgZcEngWordCommonService.processWordsAsync(form.getText());
        }
        return BaseResult.success(zcQuestion.getId());
    }

    @Operation(summary = "编辑题目【单词听写】")
    @PostMapping("update/{id}")
    @SaCheckLogin
    public BaseResult<Long> updateDictation(@RequestBody EnDictation form, @PathVariable Long id) {

        // 获取题目
        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 判断是否为当前用户
        if (StpUtil.getLoginIdAsLong() != zcQuestion.getUserId()) {
            return BaseResult.code(GlobalCode.Permission_Not);
        }

        // 题目名称
        zcQuestion.setName(form.getName());

        // 题目内容
        zcQuestion.setContentJson(form);
        zcQuestion.setUpdateTime(new Date());

        pgZcQuestionService.updateById(zcQuestion);

        // 异步执行获取英语单词音标信息
        if (ObjectUtil.isNotNull(form.getText())) {
            pgZcEngWordCommonService.processWordsAsync(form.getText());
        }

        return BaseResult.success(zcQuestion.getId());
    }

    @Operation(summary = "获取题目")
    @GetMapping("detail/{id}")
    public BaseResult<EnDictation> detail(@PathVariable Long id) {

        PgZcQuestion zcQuestion = pgZcQuestionService.getById(id);

        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        EnDictation dictation = JSONUtil.toBean(JSONUtil.toJsonStr(zcQuestion.getContentJson()), EnDictation.class);

        return BaseResult.success(dictation);
    }


    @Data
    public static class ZcVlForm {

        private String imgUrl;

        @Schema(title = "识别类型", description = "0：录词、1：录课文")
        private Integer type;
    }

    @Operation(summary = "英语拍照录词")
    @PostMapping("vl/word")
    @SaCheckLogin
    public BaseResult<String> vlWord(@RequestBody ZcVlForm form) {

        // 图片预处理
        String url = ProcessImgUtil.processImg(form.imgUrl);

        // 限制频率，1分钟最多2次
        boolean isLimit = UserBehaviorUtil.isLimit(
                GlobalConstants.TENANT_OCR_LIMIT_REDIS_KEY + StpUtil.getLoginIdAsLong(),
                10,
                Duration.ofMinutes(1)
        );

        if (isLimit) {
            return BaseResult.error("您当前频率过高，请稍后等待30秒后重试");
        }

        // 识别内容
        String prompt = "";
        // 识别词语
        if (form.getType() == 0) {
            prompt = """
                    请识别图片中的英语单词或词组，并以换行为间隔进行返回，不需要返回其他任何无关内容和提示，只需要识别出的英语单词或词组就可以。
                    请过滤掉图片中和英语单词或词组无关的内容。
                    
                    【以下是示例】
                    apple
                    hello
                    submit
                    range from...to...
                    
                    """;
        }
        // 识别课文
        else if (form.getType() == 1) {
            prompt = """
                    请识别图片中的英语课文，不需要返回其他任何东西，只需要识别英语课文就可以。
                    请过滤掉图片中和英语课文无关的内容。
                    """;
        }

        LLMService llmService = getLLMService();
        GPTAnswer answer = llmService.vl(url, "", prompt);

        if (EnvUtils.isDev()) {
            log.info(answer.toString());
        }

        return BaseResult.success(answer.getAnswer());
    }

    @Operation(summary = "英语拍照录词(包括释义)")
    @PostMapping("vl/wordAndChinese")
    @SaCheckLogin
    public BaseResult<String> vlWordAndChinese(@RequestBody ZcVlForm form) {
        TimeInterval interval = DateUtil.timer();

        // 图片预处理
        String url = ProcessImgUtil.processImg(form.imgUrl);

        // 限制频率，1分钟最多2次
        boolean isLimit = UserBehaviorUtil.isLimit(
                GlobalConstants.TENANT_OCR_LIMIT_REDIS_KEY + StpUtil.getLoginIdAsLong(),
                10,
                Duration.ofMinutes(1)
        );

        if (isLimit) {
            return BaseResult.error("您当前频率过高，请稍后等待30秒后重试");
        }

        // 识别内容
        String prompt = "";
        // 识别词语
        if (form.getType() == 0) {
            prompt = """
                    请识别图片中的英语单词，不需要返回其他任何无关内容和提示，只需要识别出的英语单词就可以。
                    请过滤掉图片中和英语单词无关的内容。
                    
                    【请以下格式返回，一行一个单词】
                    识别的单词[空格间距]图片中出现的汉语，如果没有则不需要
                    
                    【以下是示例】
                    apple 苹果
                    hello
                    submit 提交
                    """;
        }
        // 识别课文
        else if (form.getType() == 1) {
            prompt = """
                    请识别图片中的英语课文，不需要返回其他任何东西，只需要识别英语课文就可以。
                    请过滤掉图片中和英语课文无关的内容。
                    """;
        }

        LLMService llmService = getLLMService();
        GPTAnswer answer = llmService.vl(url, "", prompt);

        if (EnvUtils.isDev()) {
            log.info(answer.toString());
        }

        log.info("英语拍照录词：用时：{}", interval.intervalPretty());

        return BaseResult.success(answer.getAnswer());
    }


    @Data
    public static class EnWordAudioForm {

        @Schema(title = "单词")
        private String word;

        @Schema(title = "发音类型", description = "1：英式，2：美式")
        private Integer type;

    }

    // http://dict.youdao.com/dictvoice?audio=apple&type=1

    @Operation(summary = "获取单个单词的发音")
    @PostMapping("getWordAudio")
    public BaseResult<String> getWordAudio(@RequestBody EnWordAudioForm form) {

        // 从数据库里查
        PgZcEngWordCommon word = pgZcEngWordCommonService.getOne(new LambdaQueryWrapper<PgZcEngWordCommon>()
                .eq(PgZcEngWordCommon::getWord, form.getWord())
                .last("limit 1")
        );

        if (ObjectUtil.isNull(word)) {
            word = new PgZcEngWordCommon();
            word.setWord(form.getWord());
            word.setStatus(GenerateStatusEnum.Init);
            pgZcEngWordCommonService.save(word);
        }
        // 生成单词发音
        pgZcEngWordCommonService.getEnWordAudio(word);

        String audioUrl;
        // -- 英式
        if (form.getType() == 1) {
            audioUrl = word.getUkAudioUrl();
        }
        // -- 美式
        else {
            audioUrl = word.getUsAudioUrl();
        }

        return BaseResult.success(audioUrl);
    }


    @Data
    public static class EngWordAudioForm {

        @Schema(title = "单词")
        private List<String> words;

        @Schema(title = "发音类型", description = "1：英式，2：美式")
        private Integer type;

        @Schema(title = "顺序", description = "1：顺序，2：乱序")
        private Integer orderType;

    }

    @Operation(summary = "进行英语单词语音播报")
    @PostMapping("audio")
    public BaseResult<List<PgZcEngWordCommonVO>> audio(@RequestBody EngWordAudioForm form) {

        // 初始化返回数据
        List<PgZcEngWordCommonVO> wordList = new ArrayList<>();

        List<String> words = form.getWords();

        if (CollUtil.isEmpty(words)) {
            return BaseResult.success(wordList);
        }

        // 乱序
        if (form.getOrderType() == 2) {
            Collections.shuffle(words);
        }

        words.forEach(word -> {

            PgZcEngWordCommon one = pgZcEngWordCommonService.getOne(new LambdaQueryWrapper<PgZcEngWordCommon>()
                    .eq(PgZcEngWordCommon::getWord, word)
                    .last("limit 1")
            );
            if (ObjectUtil.isNotNull(one)) {

                // 是否返回
                boolean shouldInclude = true;
                if (ObjectUtil.isNotNull(form.getType())) {
                    if (form.getType() == 1 && StrUtil.isBlank(one.getUkAudioUrl())) {
                        shouldInclude = false;
                    } else if (form.getType() == 2 && StrUtil.isBlank(one.getUsAudioUrl())) {
                        shouldInclude = false;
                    }
                }
                if (shouldInclude) {
                    PgZcEngWordCommonVO wordCommonVO = BeanUtil.copyProperties(one, PgZcEngWordCommonVO.class);
                    wordList.add(wordCommonVO);
                }
            }
        });

        return BaseResult.success(wordList);
    }

}
