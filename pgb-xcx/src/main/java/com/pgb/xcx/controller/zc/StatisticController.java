package com.pgb.xcx.controller.zc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.houbb.pinyin.api.impl.Pinyin;
import com.pgb.common.core.global.BaseResult;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.db.PgZcAnswerService;
import com.pgb.service.db.PgZcHomeworkService;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/8/6 17:19
 */
@Tag(name = "用户端/字词/统计")
@RestController("UserZcStatisticController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/statistic")
@RequiredArgsConstructor
@Slf4j
public class StatisticController {

    private final PgHomeworkService pgHomeworkService;

    private final PgZcAnswerService pgZcAnswerService;

    @Data
    @Schema(title = "错别字统计")
    public static class WrongWordInfo {

        @Schema(title = "词语")
        private String word;

        @Schema(title = "错误频率")
        private Integer frequency;

    }

    @Operation(summary = "【看拼音写词语】高频错别字统计")
    @PostMapping("wrongWord/{homeworkId}")
    public BaseResult<List<WrongWordInfo>> wrongWord(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        List<PgZcAnswer> zcAnswers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                .eq(PgZcAnswer::getZcHomeworkId, homeworkId)
                .eq(PgZcAnswer::getDeleted, false)
        );
        zcAnswers = CollUtil.distinct(zcAnswers, PgZcAnswer::getStudentId, false);

        zcAnswers.forEach(zcAnswer -> {

            PinyinAndWordResult result = JSONUtil.toBean(zcAnswer.getCorrectResult().toString(), PinyinAndWordResult.class);


        });

        return BaseResult.success();
    }


}
