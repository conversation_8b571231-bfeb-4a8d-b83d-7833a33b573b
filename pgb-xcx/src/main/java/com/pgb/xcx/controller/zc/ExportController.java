package com.pgb.xcx.controller.zc;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.answer.batch.PgZcAnswerBatch;
import com.pgb.service.domain.zc.homework.PgZcHomework;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.ExportStatusEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/23 17:55
 */
@Tag(name = "用户端/字词/导出")
@RestController("UserZcExportController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/export")
@RequiredArgsConstructor
@Slf4j
public class ExportController {

    private final PgZcAnswerService pgZcAnswerService;

    private final PgExportRecordService pgExportRecordService;

    private final PgZcHomeworkService pgZcHomeworkService;

    private final PgStudentService pgStudentService;

    private final PgZcAnswerBatchService pgZcAnswerBatchService;

    @Operation(summary = "导出字词批改记录")
    @GetMapping("{zcAnswerId}")
    public ResponseEntity<byte[]> exportWord(@PathVariable Long zcAnswerId) {

        PgZcAnswer zcAnswer = pgZcAnswerService.getById(zcAnswerId);

        String fileName;
        // 若备注不为空
        if (StrUtil.isNotBlank(zcAnswer.getName())) {
            String name = ReUtil.delAll("[^\\u4e00-\\u9fa5a-zA-Z\\d_《》]", zcAnswer.getName());
            fileName = URLEncoder.encode(name + "_" + DateUtil.format(new Date(), "MM-dd") + "_字词批改报告", StandardCharsets.UTF_8);
        } else {
            // 导出的文件名
            fileName = URLEncoder.encode(DateUtil.format(new Date(), "MM-dd") + "_字词批改报告", StandardCharsets.UTF_8);
        }

        // 导出字词pdf
        File exportPdf = pgExportRecordService.getZcExportPdf(zcAnswer, fileName);

        // 构建导出所需参数
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition"); // 必须设置，否则无法获取文件名
        headers.set("Content-Disposition", "attachment;filename=" + exportPdf.getName());
        headers.set("Content-Type", "application/pdf");

        // 读取文件
        byte[] pdfBytes = FileUtil.readBytes(exportPdf);

        // 删除临时文件
        FileUtil.del(exportPdf);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.valueOf("application/pdf"))
                .body(pdfBytes);
    }

    @Operation(summary = "导出字词批改记录图片")
    @GetMapping("img/{zcAnswerId}")
    public  ResponseEntity<byte[]> exportImg(@PathVariable Long zcAnswerId) {

        PgZcAnswer zcAnswer = pgZcAnswerService.getById(zcAnswerId);

        String fileName;
        if (StrUtil.isNotBlank(zcAnswer.getName())) {
            String name = ReUtil.delAll("[^\\u4e00-\\u9fa5a-zA-Z\\d_《》]", zcAnswer.getName());
            fileName = URLEncoder.encode(name + "_" + DateUtil.format(new Date(), "MM-dd") + "_字词批改图片", StandardCharsets.UTF_8);
        } else {
            fileName = URLEncoder.encode(DateUtil.format(new Date(), "MM-dd") + "_字词批改图片", StandardCharsets.UTF_8);
        }

        File exportImage = pgExportRecordService.getZcExportImage(zcAnswer, fileName);

        // 构建响应头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Control-Expose-Headers", "Content-Disposition");
        headers.set("Content-Disposition", "attachment;filename=" + exportImage.getName());
        headers.setContentType(MediaType.IMAGE_JPEG); // 设置为 JPEG 格式

        // 读取文件内容
        byte[] imageBytes = FileUtil.readBytes(exportImage);

        // 删除临时文件
        FileUtil.del(exportImage);

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.IMAGE_JPEG)
                .body(imageBytes);
    }

    @Operation(summary = "根据批次导出")
    @GetMapping("batch/{batchId}")
    @SaCheckLogin
    public BaseResult<PgExportRecord> exportBatch(@PathVariable Long batchId) {

        PgZcAnswerBatch batch = pgZcAnswerBatchService.getById(batchId);

        if (ObjectUtil.isNull(batch)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<Long> zcAnswerIds = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                        .select(PgZcAnswer::getId)
                        .eq(PgZcAnswer::getDeleted, false)
                        .eq(PgZcAnswer::getBatchId, batchId)
                        // 批改完成
                        .ne(PgZcAnswer::getStatus, CorrectStatusEnum.Uploaded)
                        .orderByDesc(PgZcAnswer::getCreateTime))
                .stream()
                .map(PgZcAnswer::getId)
                .toList();

        if (zcAnswerIds.isEmpty()) {
            return BaseResult.error("作业还未批改完成，请稍等");
        }

        String md5 = pgZcAnswerService.getImgMd5(zcAnswerIds, batch.getUserId());
        PgExportRecord record = pgExportRecordService.getOne(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getUserId, batch.getUserId())
                .eq(PgExportRecord::getMd5, md5)
                .last("LIMIT 1"));

        // 有记录
        if (ObjectUtil.isNotNull(record)) {

            log.info("存在导出记录: {} : {}", record.getId(), record.getZipUrl());

            record.setCreateTime(new Date());
            pgExportRecordService.updateById(record);

            return BaseResult.success(record);
        }
        // 没记录，插入记录
        record = new PgExportRecord();
        record.setUserId(batch.getUserId());
        record.setTotalNum(zcAnswerIds.size());
        record.setCreateTime(new Date());
        record.setMd5(md5);
        record.setStatus(ExportStatusEnum.Queuing);
        record.setAnswerIds(CollUtil.join(zcAnswerIds, ";"));
        record.setType(1);
        // 名称默认时间信息
        record.setName(DateUtil.format(new Date(), "yyyy-MM-dd") + "—字词批改报告导出");
        pgExportRecordService.save(record);

        // TODO 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_ZC_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());

        // 队列请求成功后 再返回
        return BaseResult.success(record);
    }

    @Deprecated
    @Operation(summary = "根据班级作业导出")
    @GetMapping("class/{zcHomeworkId}")
    @SaCheckLogin
    public BaseResult<PgExportRecord> export(@PathVariable Long zcHomeworkId) {

        PgZcHomework zcHomework = pgZcHomeworkService.getById(zcHomeworkId);

        if (ObjectUtil.isNull(zcHomework)) {
            return BaseResult.error("作业不存在");
        }

        // 获取班级学生
        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, zcHomework.getClassId()))
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.error("班级没有关联学生！");
        }

        List<Long> zcAnswerIds = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                        .select(PgZcAnswer::getId)
                        .eq(PgZcAnswer::getDeleted, false)
                        .eq(PgZcAnswer::getZcHomeworkId, zcHomeworkId)
                        .in(PgZcAnswer::getStudentId, studentIds)
                        .ne(PgZcAnswer::getStatus, CorrectStatusEnum.Uploaded))
                .stream()
                .map(PgZcAnswer::getId)
                .toList();

        if (zcAnswerIds.isEmpty()) {
            return BaseResult.error("作业还未批改完成，请稍等");
        }

        String md5 = pgZcAnswerService.getImgMd5(zcAnswerIds, StpUtil.getLoginIdAsLong());

        PgExportRecord record = pgExportRecordService.getOne(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgExportRecord::getMd5, md5)
                .last("LIMIT 1")
        );

        // 有记录
        if (ObjectUtil.isNotNull(record)) {
            log.info("存在导出记录: {}", record.getZipUrl());

            record.setCreateTime(new Date());
            pgExportRecordService.updateById(record);
            return BaseResult.success(record);
        }

        // 没有记录 插入记录
        record = new PgExportRecord();
        record.setHomeworkId(zcHomeworkId);
        record.setUserId(StpUtil.getLoginIdAsLong());
        record.setTotalNum(zcAnswerIds.size());
        record.setCreateTime(new Date());
        record.setMd5(md5);
        record.setStatus(ExportStatusEnum.Queuing);
        record.setType(1);
        // 名称
        record.setName(zcHomework.getName() + "—字词批改报告导出");
        record.setAnswerIds(CollUtil.join(zcAnswerIds, ";"));
        pgExportRecordService.save(record);

        // TODO 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_ZC_BATCH_EXPORT_WORD_QUEUE.name(), record.getId());
        return BaseResult.success(record);

    }


}
